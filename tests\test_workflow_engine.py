"""
Comprehensive test suite for the Workflow Engine
Tests BMAD methodology implementation and workflow execution
"""

import pytest
import asyncio
import json
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

# Import workflow engine components
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from workflow_engine import WorkflowEngine, WorkflowPhase, WorkflowStep, WorkflowContext


class TestWorkflowStep:
    """Test individual workflow steps"""
    
    def test_step_creation(self):
        """Test creating a workflow step"""
        step = WorkflowStep(
            name="test_step",
            description="Test step description",
            agent_role="analyst",
            inputs=["requirements"],
            outputs=["analysis.md"],
            dependencies=["previous_step"]
        )
        
        assert step.name == "test_step"
        assert step.description == "Test step description"
        assert step.agent_role == "analyst"
        assert step.inputs == ["requirements"]
        assert step.outputs == ["analysis.md"]
        assert step.dependencies == ["previous_step"]
        assert step.status == "pending"
    
    def test_step_validation(self):
        """Test step validation"""
        step = WorkflowStep(
            name="test_step",
            description="Test step",
            agent_role="analyst"
        )
        
        # Valid step should pass validation
        assert step.validate() is True
        
        # Invalid step (empty name) should fail
        step.name = ""
        assert step.validate() is False
    
    def test_step_execution_tracking(self):
        """Test tracking step execution"""
        step = WorkflowStep(
            name="test_step",
            description="Test step",
            agent_role="analyst"
        )
        
        # Initially pending
        assert step.status == "pending"
        assert step.start_time is None
        assert step.end_time is None
        
        # Start execution
        step.start_execution()
        assert step.status == "running"
        assert step.start_time is not None
        assert step.end_time is None
        
        # Complete execution
        step.complete_execution(success=True, outputs=["output.md"])
        assert step.status == "completed"
        assert step.end_time is not None
        assert step.outputs == ["output.md"]
        
        # Calculate duration
        duration = step.get_duration()
        assert duration is not None
        assert duration.total_seconds() >= 0
    
    def test_step_failure_handling(self):
        """Test handling step failures"""
        step = WorkflowStep(
            name="test_step",
            description="Test step",
            agent_role="analyst"
        )
        
        step.start_execution()
        step.complete_execution(success=False, error="Test error")
        
        assert step.status == "failed"
        assert step.error == "Test error"
        assert step.end_time is not None


class TestWorkflowPhase:
    """Test workflow phases"""
    
    def test_phase_creation(self):
        """Test creating a workflow phase"""
        steps = [
            WorkflowStep("step1", "First step", "analyst"),
            WorkflowStep("step2", "Second step", "architect")
        ]
        
        phase = WorkflowPhase(
            name="analysis_phase",
            description="Analysis phase",
            steps=steps,
            parallel=False
        )
        
        assert phase.name == "analysis_phase"
        assert phase.description == "Analysis phase"
        assert len(phase.steps) == 2
        assert phase.parallel is False
        assert phase.status == "pending"
    
    def test_phase_dependency_resolution(self):
        """Test resolving step dependencies within a phase"""
        step1 = WorkflowStep("step1", "First step", "analyst")
        step2 = WorkflowStep("step2", "Second step", "architect", dependencies=["step1"])
        step3 = WorkflowStep("step3", "Third step", "developer", dependencies=["step1", "step2"])
        
        phase = WorkflowPhase(
            name="test_phase",
            description="Test phase",
            steps=[step1, step2, step3]
        )
        
        execution_order = phase.get_execution_order()
        
        # step1 should be first (no dependencies)
        assert execution_order[0] == step1
        # step2 should be second (depends on step1)
        assert execution_order[1] == step2
        # step3 should be last (depends on both step1 and step2)
        assert execution_order[2] == step3
    
    def test_phase_parallel_execution(self):
        """Test parallel execution capability"""
        step1 = WorkflowStep("step1", "First step", "analyst")
        step2 = WorkflowStep("step2", "Second step", "architect")
        step3 = WorkflowStep("step3", "Third step", "developer")
        
        phase = WorkflowPhase(
            name="parallel_phase",
            description="Parallel phase",
            steps=[step1, step2, step3],
            parallel=True
        )
        
        assert phase.can_run_parallel() is True
        
        # All steps without dependencies can run in parallel
        parallel_steps = phase.get_parallel_steps()
        assert len(parallel_steps) == 3
    
    def test_phase_progress_tracking(self):
        """Test tracking phase progress"""
        steps = [
            WorkflowStep("step1", "First step", "analyst"),
            WorkflowStep("step2", "Second step", "architect"),
            WorkflowStep("step3", "Third step", "developer")
        ]
        
        phase = WorkflowPhase(
            name="test_phase",
            description="Test phase",
            steps=steps
        )
        
        # Initially 0% complete
        assert phase.get_progress() == 0.0
        
        # Complete first step
        steps[0].status = "completed"
        assert phase.get_progress() == pytest.approx(33.33, rel=1e-2)
        
        # Complete second step
        steps[1].status = "completed"
        assert phase.get_progress() == pytest.approx(66.67, rel=1e-2)
        
        # Complete all steps
        steps[2].status = "completed"
        assert phase.get_progress() == 100.0
        
        # Phase should be completed
        assert phase.is_completed() is True


class TestWorkflowContext:
    """Test workflow execution context"""
    
    def test_context_creation(self):
        """Test creating workflow context"""
        context = WorkflowContext(
            project_id="test_project",
            project_path="/test/path",
            project_type="fullstack",
            prompt="Create a web application"
        )
        
        assert context.project_id == "test_project"
        assert context.project_path == "/test/path"
        assert context.project_type == "fullstack"
        assert context.prompt == "Create a web application"
        assert context.variables == {}
        assert context.artifacts == {}
    
    def test_context_variables(self):
        """Test context variable management"""
        context = WorkflowContext(
            project_id="test_project",
            project_path="/test/path"
        )
        
        # Set variables
        context.set_variable("database_type", "postgresql")
        context.set_variable("api_framework", "fastapi")
        
        assert context.get_variable("database_type") == "postgresql"
        assert context.get_variable("api_framework") == "fastapi"
        assert context.get_variable("nonexistent") is None
        assert context.get_variable("nonexistent", "default") == "default"
    
    def test_context_artifacts(self):
        """Test context artifact management"""
        context = WorkflowContext(
            project_id="test_project",
            project_path="/test/path"
        )
        
        # Add artifacts
        context.add_artifact("requirements.md", "/path/to/requirements.md", "document")
        context.add_artifact("architecture.md", "/path/to/architecture.md", "document")
        
        assert len(context.artifacts) == 2
        assert "requirements.md" in context.artifacts
        assert context.artifacts["requirements.md"]["path"] == "/path/to/requirements.md"
        assert context.artifacts["requirements.md"]["type"] == "document"
    
    def test_context_serialization(self):
        """Test context serialization"""
        context = WorkflowContext(
            project_id="test_project",
            project_path="/test/path",
            project_type="fullstack"
        )
        
        context.set_variable("test_var", "test_value")
        context.add_artifact("test.md", "/test.md", "document")
        
        # Serialize to dict
        data = context.to_dict()
        
        assert data["project_id"] == "test_project"
        assert data["project_path"] == "/test/path"
        assert data["project_type"] == "fullstack"
        assert data["variables"]["test_var"] == "test_value"
        assert "test.md" in data["artifacts"]
        
        # Create from dict
        new_context = WorkflowContext.from_dict(data)
        
        assert new_context.project_id == "test_project"
        assert new_context.get_variable("test_var") == "test_value"
        assert "test.md" in new_context.artifacts


class TestWorkflowEngine:
    """Test the main workflow engine"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        import shutil
        shutil.rmtree(temp_dir)
    
    def test_engine_initialization(self):
        """Test workflow engine initialization"""
        engine = WorkflowEngine()
        
        assert engine.workflows == {}
        assert engine.active_executions == {}
        assert engine.execution_history == []
    
    def test_workflow_registration(self):
        """Test registering workflows"""
        engine = WorkflowEngine()
        
        # Create test workflow
        steps = [
            WorkflowStep("analysis", "Requirements analysis", "analyst"),
            WorkflowStep("design", "System design", "architect")
        ]
        phase = WorkflowPhase("development", "Development phase", steps)
        
        workflow_config = {
            "name": "test_workflow",
            "description": "Test workflow",
            "phases": [phase],
            "project_types": ["fullstack"]
        }
        
        engine.register_workflow("test_workflow", workflow_config)
        
        assert "test_workflow" in engine.workflows
        assert engine.workflows["test_workflow"]["name"] == "test_workflow"
    
    def test_workflow_selection(self):
        """Test selecting appropriate workflow for project type"""
        engine = WorkflowEngine()
        
        # Register workflows for different project types
        fullstack_workflow = {
            "name": "fullstack_workflow",
            "description": "Full stack workflow",
            "phases": [],
            "project_types": ["fullstack", "web"]
        }
        
        api_workflow = {
            "name": "api_workflow",
            "description": "API workflow",
            "phases": [],
            "project_types": ["api", "backend"]
        }
        
        engine.register_workflow("fullstack", fullstack_workflow)
        engine.register_workflow("api", api_workflow)
        
        # Test workflow selection
        selected = engine.select_workflow("fullstack")
        assert selected["name"] == "fullstack_workflow"
        
        selected = engine.select_workflow("api")
        assert selected["name"] == "api_workflow"
        
        # Test fallback to default
        selected = engine.select_workflow("unknown_type")
        assert selected is not None  # Should return a default workflow
    
    @pytest.mark.asyncio
    async def test_workflow_execution(self, temp_dir):
        """Test executing a complete workflow"""
        engine = WorkflowEngine()
        
        # Create mock agent executor
        mock_executor = Mock()
        mock_executor.execute = AsyncMock(return_value={
            "success": True,
            "outputs": ["test_output.md"],
            "summary": "Step completed successfully"
        })
        
        # Create test workflow
        steps = [
            WorkflowStep("step1", "First step", "analyst", outputs=["analysis.md"]),
            WorkflowStep("step2", "Second step", "architect", outputs=["design.md"])
        ]
        phase = WorkflowPhase("test_phase", "Test phase", steps)
        
        workflow_config = {
            "name": "test_workflow",
            "description": "Test workflow",
            "phases": [phase],
            "project_types": ["test"]
        }
        
        engine.register_workflow("test", workflow_config)
        
        # Create execution context
        context = WorkflowContext(
            project_id="test_project",
            project_path=temp_dir,
            project_type="test",
            prompt="Test project"
        )
        
        # Mock agent creation
        with patch('src.agent_executors.create_agent_executor', return_value=mock_executor):
            execution_id = await engine.execute_workflow("test", context)
            
            assert execution_id is not None
            assert execution_id in engine.active_executions
            
            # Wait for execution to complete
            result = await engine.wait_for_completion(execution_id, timeout=10)
            
            assert result["success"] is True
            assert result["phases_completed"] == 1
            assert execution_id not in engine.active_executions
            assert execution_id in [e["execution_id"] for e in engine.execution_history]
